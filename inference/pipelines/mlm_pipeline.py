# Copyright 2024 onwards Answer.AI, LightOn, and contributors
# License: Apache-2.0

import os
import sys
from pathlib import Path
from typing import Dict, Any, List, Optional, Union
import torch
import logging

# Add project root to path for imports
sys.path.append(str(Path(__file__).parent.parent.parent))

# Handle imports - try relative first, then absolute
try:
    from .base_pipeline import BasePipeline
    from ..config.model_config import ModelConfig
    from ..config.inference_config import InferenceConfig
    from ..utils.validation_utils import ValidationUtils
    from ..utils.padding_utils import PaddingUtils
except ImportError:
    # If relative imports fail, try direct module imports
    inference_dir = Path(__file__).parent.parent
    sys.path.insert(0, str(inference_dir))
    
    from pipelines.base_pipeline import BasePipeline
    from config.model_config import ModelConfig
    from config.inference_config import InferenceConfig
    from utils.validation_utils import ValidationUtils
    from utils.padding_utils import PaddingUtils

logger = logging.getLogger(__name__)


class MLMPipeline(BasePipeline):
    """
    Pipeline for Masked Language Modeling (MLM) inference.
    
    Supports mask-fill tasks with automatic adaptation to padding/unpadding
    configuration based on the training setup.
    """
    
    def __init__(
        self,
        model_config: ModelConfig,
        inference_config: InferenceConfig,
        checkpoint_path: Optional[str] = None
    ):
        """
        Initialize MLM Pipeline.
        
        Args:
            model_config: Model configuration
            inference_config: Inference configuration
            checkpoint_path: Optional path to model checkpoint
        """
        super().__init__(model_config, inference_config, checkpoint_path)
        logger.info("MLM Pipeline initialized")
    
    def predict(
        self,
        inputs: Union[str, List[str]],
        top_k: Optional[int] = None,
        top_p: Optional[float] = None,
        temperature: float = 1.0,
        return_all_predictions: Optional[bool] = None
    ) -> Union[Dict[str, Any], List[Dict[str, Any]]]:
        """
        Predict masked tokens in input text(s).
        
        Args:
            inputs: Input text(s) containing [MASK] tokens
            top_k: Number of top predictions to return (uses config default if None)
            top_p: Nucleus sampling threshold (uses config default if None)
            temperature: Sampling temperature
            return_all_predictions: Return all mask predictions vs just top ones
            
        Returns:
            Prediction results for masked tokens
        """
        # Handle single string input
        single_input = isinstance(inputs, str)
        if single_input:
            inputs = [inputs]
        
        # Use config defaults if not provided
        top_k = top_k or self.inference_config.mlm_top_k
        top_p = top_p or self.inference_config.mlm_top_p
        return_all_predictions = return_all_predictions or self.inference_config.mlm_return_all_predictions
        
        # Prepare model inputs
        model_inputs = self._prepare_inputs(inputs, task_type="mlm")
        
        # Run model inference
        model_outputs = self._run_model(model_inputs)
        
        # Extract logits
        logits = model_outputs.logits
        
        # Process predictions based on padding configuration
        predictions = self._process_mlm_predictions(
            logits=logits,
            model_inputs=model_inputs,
            original_texts=inputs,
            top_k=top_k,
            top_p=top_p,
            temperature=temperature,
            return_all_predictions=return_all_predictions
        )
        
        # Return single result if single input
        if single_input and predictions:
            return predictions[0]
        
        return predictions
    
    def predict_masks_in_context(
        self,
        template: str,
        contexts: List[str],
        top_k: Optional[int] = None,
        **kwargs
    ) -> List[Dict[str, Any]]:
        """
        Predict masks for the same template in different contexts.
        
        Args:
            template: Template string with [MASK] tokens
            contexts: List of context strings to insert into template
            top_k: Number of top predictions
            **kwargs: Additional prediction parameters
            
        Returns:
            List of predictions for each context
        """
        # Create input texts by formatting template with contexts
        input_texts = []
        for context in contexts:
            # Simple template formatting - can be enhanced
            if "{context}" in template:
                text = template.format(context=context)
            else:
                text = f"{context} {template}"
            input_texts.append(text)
        
        return self.predict(input_texts, top_k=top_k, **kwargs)
    
    def fill_mask(
        self,
        text: str,
        mask_token: str = "[MASK]",
        return_top_k: int = 5
    ) -> List[Dict[str, Any]]:
        """
        Simple mask filling interface similar to Hugging Face pipelines.
        
        Args:
            text: Input text with mask tokens
            mask_token: Mask token to replace
            return_top_k: Number of top predictions to return
            
        Returns:
            List of predictions with scores
        """
        if mask_token not in text:
            raise ValueError(f"No {mask_token} token found in input text")
        
        predictions = self.predict(text, top_k=return_top_k)
        
        # Reformat to match HF pipeline style
        results = []
        if isinstance(predictions, dict) and "mask_predictions" in predictions:
            for mask_pred in predictions["mask_predictions"]:
                for pred in mask_pred["predictions"][:return_top_k]:
                    filled_text = text.replace(mask_token, pred["token"], 1)
                    results.append({
                        "sequence": filled_text,
                        "token": pred["token"],
                        "token_str": pred["token"],
                        "score": pred["probability"]
                    })
        
        return results
    
    def _process_mlm_predictions(
        self,
        logits: torch.Tensor,
        model_inputs: Dict[str, torch.Tensor],
        original_texts: List[str],
        top_k: int,
        top_p: float,
        temperature: float,
        return_all_predictions: bool
    ) -> List[Dict[str, Any]]:
        """
        Process model logits into formatted predictions.
        
        Args:
            logits: Model output logits
            model_inputs: Original model inputs
            original_texts: Original input texts
            top_k: Number of top predictions
            top_p: Nucleus sampling threshold
            temperature: Sampling temperature
            return_all_predictions: Whether to return all predictions
            
        Returns:
            List of formatted prediction dictionaries
        """
        input_ids = model_inputs["input_ids"]
        
        # Handle padding/unpadding configuration
        if self.model_config.is_unpadded and not self.model_config.pad_logits:
            # Logits are unpadded, need special handling
            return self._process_unpadded_predictions(
                logits, model_inputs, original_texts, top_k, top_p, temperature, return_all_predictions
            )
        else:
            # Logits are padded, standard processing
            return self._process_padded_predictions(
                logits, input_ids, original_texts, top_k, top_p, temperature, return_all_predictions
            )
    
    def _process_padded_predictions(
        self,
        logits: torch.Tensor,
        input_ids: torch.Tensor,
        original_texts: List[str],
        top_k: int,
        top_p: float,
        temperature: float,
        return_all_predictions: bool
    ) -> List[Dict[str, Any]]:
        """Process predictions from padded logits."""
        batch_size = input_ids.shape[0]
        results = []
        
        # Find mask positions
        mask_positions = self.input_processor.find_mask_positions(input_ids)
        
        # Decode predictions
        predictions = self.input_processor.decode_predictions(
            logits=logits,
            input_ids=input_ids,
            mask_positions=mask_positions,
            top_k=top_k,
            top_p=top_p,
            temperature=temperature
        )
        
        # Group predictions by batch
        batch_predictions = {i: [] for i in range(batch_size)}
        for pred in predictions:
            batch_idx = pred["position"][0] if isinstance(pred["position"], tuple) else 0
            batch_predictions[batch_idx].append(pred)
        
        # Format results
        for i, text in enumerate(original_texts):
            result = {
                "input_text": text,
                "mask_predictions": batch_predictions.get(i, []),
                "num_masks": len(batch_predictions.get(i, []))
            }
            
            # Add summary if not returning all predictions
            if not return_all_predictions and result["mask_predictions"]:
                result["top_predictions"] = [
                    {
                        "position": pred["position"],
                        "top_token": pred["predictions"][0] if pred["predictions"] else None
                    }
                    for pred in result["mask_predictions"]
                ]
            
            results.append(result)
        
        return results
    
    def _process_unpadded_predictions(
        self,
        logits: torch.Tensor,
        model_inputs: Dict[str, torch.Tensor],
        original_texts: List[str],
        top_k: int,
        top_p: float,
        temperature: float,
        return_all_predictions: bool
    ) -> List[Dict[str, Any]]:
        """Process predictions from unpadded logits."""
        # For unpadded predictions, we need to map back to original sequences
        cu_seqlens = model_inputs["cu_seqlens"]
        indices = model_inputs["indices"]
        input_ids = model_inputs["input_ids"]
        
        # Decode predictions using simplified approach for unpadded
        predictions = self.input_processor.decode_predictions(
            logits=logits,
            input_ids=input_ids,
            top_k=top_k,
            top_p=top_p,
            temperature=temperature
        )
        
        # Map predictions back to batch sequences
        batch_size = cu_seqlens.shape[0] - 1
        results = []
        
        for i, text in enumerate(original_texts):
            # Simple mapping - in production would need more sophisticated approach
            relevant_predictions = [p for p in predictions if i == 0]  # Simplified
            
            result = {
                "input_text": text,
                "mask_predictions": relevant_predictions,
                "num_masks": len(relevant_predictions)
            }
            
            if not return_all_predictions and relevant_predictions:
                result["top_predictions"] = [
                    {
                        "position": pred["position"],
                        "top_token": pred["predictions"][0] if pred["predictions"] else None
                    }
                    for pred in relevant_predictions
                ]
            
            results.append(result)
        
        return results
    
    def evaluate_perplexity(
        self,
        texts: List[str],
        mask_probability: float = 0.15
    ) -> List[Dict[str, Any]]:
        """
        Evaluate perplexity of texts by masking tokens and measuring prediction confidence.
        
        Args:
            texts: Input texts to evaluate
            mask_probability: Probability of masking each token
            
        Returns:
            List of perplexity evaluations
        """
        results = []
        
        for text in texts:
            # Tokenize text
            tokens = self.input_processor.tokenizer.tokenize(text)
            
            if len(tokens) == 0:
                results.append({"text": text, "perplexity": float('inf'), "error": "Empty tokenization"})
                continue
            
            total_log_prob = 0.0
            num_predictions = 0
            
            # Mask each token and predict it
            for i, token in enumerate(tokens):
                # Create masked version
                masked_tokens = tokens.copy()
                masked_tokens[i] = "[MASK]"
                masked_text = self.input_processor.tokenizer.convert_tokens_to_string(masked_tokens)
                
                try:
                    # Predict the masked token
                    predictions = self.predict(masked_text, top_k=1)
                    
                    if predictions and "mask_predictions" in predictions:
                        for mask_pred in predictions["mask_predictions"]:
                            if mask_pred["predictions"]:
                                predicted_token = mask_pred["predictions"][0]["token"]
                                prob = mask_pred["predictions"][0]["probability"]
                                
                                if predicted_token.strip() == token.strip():
                                    total_log_prob += torch.log(torch.tensor(prob + 1e-10)).item()
                                    num_predictions += 1
                
                except Exception as e:
                    logger.warning(f"Failed to predict token {i} in text: {e}")
                    continue
            
            # Calculate perplexity
            if num_predictions > 0:
                avg_log_prob = total_log_prob / num_predictions
                perplexity = torch.exp(-torch.tensor(avg_log_prob)).item()
            else:
                perplexity = float('inf')
            
            results.append({
                "text": text,
                "perplexity": perplexity,
                "num_tokens_evaluated": num_predictions,
                "total_tokens": len(tokens)
            })
        
        return results 