# Copyright 2024 onwards Answer.AI, LightOn, and contributors
# License: Apache-2.0

import os
import sys
from pathlib import Path
from typing import Dict, Any, List, Optional, Union, Tuple
import torch
import numpy as np
import logging
from transformers import AutoTokenizer
import torch.nn.functional as F

# Add project root to path for imports
sys.path.append(str(Path(__file__).parent.parent.parent))

# Handle imports - try relative first, then absolute
try:
    from ..config.model_config import ModelConfig
    from ..config.inference_config import InferenceConfig
except ImportError:
    # If relative imports fail, try direct module imports
    inference_dir = Path(__file__).parent.parent
    sys.path.insert(0, str(inference_dir))
    
    from config.model_config import ModelConfig
    from config.inference_config import InferenceConfig

logger = logging.getLogger(__name__)


class InputProcessor:
    """
    Handles text tokenization and sequence preparation for inference.
    
    Adapts to padding/unpadding configuration automatically based on model config.
    Supports both MLM and embedding generation tasks.
    """
    
    def __init__(self, model_config: ModelConfig, inference_config: InferenceConfig):
        """
        Initialize InputProcessor.
        
        Args:
            model_config: Model configuration containing tokenizer info
            inference_config: Inference configuration
        """
        self.model_config = model_config
        self.inference_config = inference_config
        self.tokenizer = None
        
        self._load_tokenizer()
        
    def _load_tokenizer(self):
        """Load the tokenizer from the model configuration."""
        try:
            logger.info(f"Loading tokenizer: {self.model_config.tokenizer_name}")
            self.tokenizer = AutoTokenizer.from_pretrained(
                self.model_config.tokenizer_name,
                use_fast=True,
                trust_remote_code=True
            )
            
            # Ensure we have required special tokens
            if self.tokenizer.mask_token is None:
                logger.warning("Tokenizer missing mask token, adding [MASK]")
                self.tokenizer.add_special_tokens({"mask_token": "[MASK]"})
            
            if self.tokenizer.pad_token is None:
                logger.warning("Tokenizer missing pad token, using unk token")
                self.tokenizer.pad_token = self.tokenizer.unk_token
                
            logger.info(f"Tokenizer loaded successfully. Vocab size: {len(self.tokenizer)}")
            
        except Exception as e:
            raise RuntimeError(f"Failed to load tokenizer {self.model_config.tokenizer_name}: {e}")
    
    def prepare_mlm_inputs(
        self, 
        texts: List[str],
        mask_token: str = "[MASK]"
    ) -> Dict[str, torch.Tensor]:
        """
        Prepare inputs for MLM inference.
        
        Args:
            texts: List of input texts containing mask tokens
            mask_token: Token to use for masking (default: "[MASK]")
            
        Returns:
            Dictionary containing input tensors for the model
        """
        if not texts:
            raise ValueError("Input texts cannot be empty")
        
        # Validate that texts contain mask tokens
        if not any(mask_token in text for text in texts):
            logger.warning("No mask tokens found in input texts")
        
        # Tokenize texts
        tokenized = self.tokenizer(
            texts,
            padding=True,
            truncation=True,
            max_length=self.inference_config.max_sequence_length,
            return_tensors="pt",
            return_attention_mask=True
        )
        
        # Convert to appropriate format based on model configuration
        if self.model_config.is_unpadded:
            return self._convert_to_unpadded(tokenized)
        else:
            return self._prepare_padded_inputs(tokenized)
    
    def prepare_embedding_inputs(self, texts: List[str]) -> Dict[str, torch.Tensor]:
        """
        Prepare inputs for embedding generation.
        
        Args:
            texts: List of input texts
            
        Returns:
            Dictionary containing input tensors for the model
        """
        if not texts:
            raise ValueError("Input texts cannot be empty")
        
        # Tokenize texts
        tokenized = self.tokenizer(
            texts,
            padding=True,
            truncation=True,
            max_length=self.inference_config.max_sequence_length,
            return_tensors="pt",
            return_attention_mask=True
        )
        
        # Convert to appropriate format based on model configuration
        if self.model_config.is_unpadded:
            return self._convert_to_unpadded(tokenized)
        else:
            return self._prepare_padded_inputs(tokenized)
    
    def _prepare_padded_inputs(self, tokenized: Dict[str, torch.Tensor]) -> Dict[str, torch.Tensor]:
        """
        Prepare inputs in padded format.
        
        Args:
            tokenized: Tokenized inputs from transformers tokenizer
            
        Returns:
            Dictionary with model inputs
        """
        inputs = {
            "input_ids": tokenized["input_ids"],
            "attention_mask": tokenized["attention_mask"]
        }
        
        # Move to target device
        device = torch.device(self.inference_config.device)
        inputs = {k: v.to(device) for k, v in inputs.items()}
        
        logger.debug(f"Prepared padded inputs: batch_size={inputs['input_ids'].shape[0]}, "
                    f"seq_len={inputs['input_ids'].shape[1]}")
        
        return inputs
    
    def _convert_to_unpadded(self, tokenized: Dict[str, torch.Tensor]) -> Dict[str, torch.Tensor]:
        """
        Convert padded inputs to unpadded format.
        
        Args:
            tokenized: Tokenized inputs from transformers tokenizer
            
        Returns:
            Dictionary with unpadded model inputs
        """
        input_ids = tokenized["input_ids"]
        attention_mask = tokenized["attention_mask"]
        
        # Import the unpadding function
        from src.bert_padding import unpad_input
        
        # Convert to unpadded format
        unpadded_ids, indices, cu_seqlens, max_seqlen = unpad_input(
            input_ids.unsqueeze(-1),  # Add feature dimension
            attention_mask
        )
        
        # Remove the added feature dimension
        unpadded_ids = unpadded_ids.squeeze(-1)
        
        inputs = {
            "input_ids": unpadded_ids,
            "attention_mask": attention_mask,  # Keep original for reference
            "indices": indices,
            "cu_seqlens": cu_seqlens,
            "max_seqlen": max_seqlen
        }
        
        # Move to target device
        device = torch.device(self.inference_config.device)
        inputs = {k: v.to(device) if torch.is_tensor(v) else v for k, v in inputs.items()}
        
        logger.debug(f"Prepared unpadded inputs: total_tokens={unpadded_ids.shape[0]}, "
                    f"batch_size={cu_seqlens.shape[0]-1}, max_seqlen={max_seqlen}")
        
        return inputs
    
    def find_mask_positions(self, input_ids: torch.Tensor) -> torch.Tensor:
        """
        Find positions of mask tokens in input sequences.
        
        Args:
            input_ids: Input token IDs
            
        Returns:
            Tensor of mask token positions
        """
        mask_token_id = self.tokenizer.mask_token_id
        if mask_token_id is None:
            raise ValueError("Tokenizer does not have a mask token")
        
        mask_positions = (input_ids == mask_token_id).nonzero(as_tuple=False)
        
        logger.debug(f"Found {mask_positions.shape[0]} mask positions")
        return mask_positions
    
    def decode_predictions(
        self, 
        logits: torch.Tensor, 
        input_ids: torch.Tensor,
        mask_positions: Optional[torch.Tensor] = None,
        top_k: Optional[int] = None,
        top_p: Optional[float] = None,
        temperature: float = 1.0
    ) -> List[Dict[str, Any]]:
        """
        Decode model predictions for MLM task.
        
        Args:
            logits: Model logits
            input_ids: Original input IDs
            mask_positions: Positions of mask tokens
            top_k: Number of top predictions to return
            top_p: Nucleus sampling threshold
            temperature: Sampling temperature
            
        Returns:
            List of prediction dictionaries
        """
        if mask_positions is None:
            mask_positions = self.find_mask_positions(input_ids)
        
        if mask_positions.shape[0] == 0:
            logger.warning("No mask positions found for decoding")
            return []
        
        # Use config defaults if not provided
        top_k = top_k or self.inference_config.mlm_top_k
        top_p = top_p or self.inference_config.mlm_top_p
        
        predictions = []
        
        # Handle unpadded vs padded logits
        if self.model_config.is_unpadded and not self.model_config.pad_logits:
            # Logits are unpadded, need to map mask positions
            predictions = self._decode_unpadded_predictions(
                logits, input_ids, mask_positions, top_k, top_p, temperature
            )
        else:
            # Logits are padded, can index directly
            predictions = self._decode_padded_predictions(
                logits, input_ids, mask_positions, top_k, top_p, temperature
            )
        
        return predictions
    
    def _decode_padded_predictions(
        self,
        logits: torch.Tensor,
        input_ids: torch.Tensor, 
        mask_positions: torch.Tensor,
        top_k: int,
        top_p: float,
        temperature: float
    ) -> List[Dict[str, Any]]:
        """Decode predictions from padded logits."""
        predictions = []
        
        for batch_idx, seq_idx in mask_positions:
            batch_idx, seq_idx = batch_idx.item(), seq_idx.item()
            
            # Get logits for this mask position
            mask_logits = logits[batch_idx, seq_idx] / temperature
            
            # Apply top-k filtering
            if top_k > 0:
                top_k_logits, top_k_indices = torch.topk(mask_logits, min(top_k, mask_logits.size(-1)))
            else:
                top_k_logits, top_k_indices = mask_logits, torch.arange(mask_logits.size(-1))
            
            # Apply top-p filtering
            if top_p < 1.0:
                sorted_logits, sorted_indices = torch.sort(top_k_logits, descending=True)
                cumulative_probs = torch.cumsum(F.softmax(sorted_logits, dim=-1), dim=-1)
                
                # Remove tokens with cumulative probability above threshold
                sorted_indices_to_remove = cumulative_probs > top_p
                sorted_indices_to_remove[1:] = sorted_indices_to_remove[:-1].clone()
                sorted_indices_to_remove[0] = 0
                
                indices_to_remove = sorted_indices[sorted_indices_to_remove]
                top_k_logits[indices_to_remove] = -float('inf')
            
            # Convert to probabilities
            probs = F.softmax(top_k_logits, dim=-1)
            
            # Get top predictions
            top_probs, top_indices_local = torch.topk(probs, min(top_k, probs.size(-1)))
            token_ids = top_k_indices[top_indices_local]
            
            # Decode tokens
            prediction = {
                "position": (batch_idx, seq_idx),
                "predictions": []
            }
            
            for token_id, prob in zip(token_ids, top_probs):
                token_text = self.tokenizer.decode([token_id.item()], skip_special_tokens=True)
                prediction["predictions"].append({
                    "token": token_text,
                    "token_id": token_id.item(),
                    "probability": prob.item()
                })
            
            predictions.append(prediction)
        
        return predictions
    
    def _decode_unpadded_predictions(
        self,
        logits: torch.Tensor,
        input_ids: torch.Tensor,
        mask_positions: torch.Tensor, 
        top_k: int,
        top_p: float,
        temperature: float
    ) -> List[Dict[str, Any]]:
        """Decode predictions from unpadded logits."""
        # For unpadded logits, we need to map mask positions to the flattened sequence
        # This requires the indices tensor from the unpadding operation
        logger.warning("Unpadded prediction decoding not fully implemented. Using simplified approach.")
        
        # Simplified approach: assume logits correspond to flattened input_ids
        predictions = []
        mask_token_id = self.tokenizer.mask_token_id
        
        # Find mask positions in flattened input
        mask_indices = (input_ids == mask_token_id).nonzero(as_tuple=False).flatten()
        
        for idx in mask_indices:
            if idx.item() >= logits.shape[0]:
                continue
                
            mask_logits = logits[idx.item()] / temperature
            
            # Apply top-k filtering
            top_k_logits, top_k_indices = torch.topk(mask_logits, min(top_k, mask_logits.size(-1)))
            
            # Convert to probabilities
            probs = F.softmax(top_k_logits, dim=-1)
            
            # Decode tokens
            prediction = {
                "position": idx.item(),
                "predictions": []
            }
            
            for token_id, prob in zip(top_k_indices, probs):
                token_text = self.tokenizer.decode([token_id.item()], skip_special_tokens=True)
                prediction["predictions"].append({
                    "token": token_text,
                    "token_id": token_id.item(),
                    "probability": prob.item()
                })
            
            predictions.append(prediction)
        
        return predictions
    
    def get_tokenizer_info(self) -> Dict[str, Any]:
        """
        Get information about the loaded tokenizer.
        
        Returns:
            Dictionary with tokenizer information
        """
        if self.tokenizer is None:
            return {"status": "not_loaded"}
        
        return {
            "name": self.model_config.tokenizer_name,
            "vocab_size": len(self.tokenizer),
            "model_max_length": self.tokenizer.model_max_length,
            "mask_token": self.tokenizer.mask_token,
            "mask_token_id": self.tokenizer.mask_token_id,
            "pad_token": self.tokenizer.pad_token,
            "pad_token_id": self.tokenizer.pad_token_id,
            "cls_token": self.tokenizer.cls_token,
            "sep_token": self.tokenizer.sep_token,
            "special_tokens": self.tokenizer.special_tokens_map,
        } 